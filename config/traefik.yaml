# Traefik Configuration for Eko Backend
# Supports API docs on port 8201, Traefik dashboard on 8202, and sticky sessions for document processing

# Global configuration
global:
  checkNewVersion: false
  sendAnonymousUsage: false

# Entry points configuration
entryPoints:
  # HTTP entry point (port 80)
  web:
    address: ":80"
    
  # HTTPS entry point (port 443)
  websecure:
    address: ":443"
    
  # API docs entry point (port 8201)
  http-api:
    address: ":8201"
    
  # Traefik dashboard entry point (port 8202)
  traefik-dashboard:
    address: ":8202"

# API configuration for dashboard
api:
  dashboard: true
  debug: true
  insecure: true  # Allow insecure connections for dashboard

# Providers configuration
providers:
  # Docker provider for service discovery
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    watch: true
    
  # File provider for additional configuration
  file:
    directory: "/etc/traefik/conf/"
    watch: true

# TLS configuration is handled by file provider in conf/tls.yaml

# Logging configuration
log:
  level: INFO
  format: json

# Access logs
accessLog:
  format: json

# Metrics configuration
metrics:
  prometheus:
    addEntryPointsLabels: true
    addServicesLabels: true

# Ping configuration for health checks
ping:
  entryPoint: "traefik-dashboard"
