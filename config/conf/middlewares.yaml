# Middleware configuration for <PERSON>raefik
# Includes security headers, CORS, and rate limiting

http:
  middlewares:
    # Default security headers
    default-headers:
      headers:
        frameDeny: true
        sslRedirect: true
        browserXssFilter: true
        contentTypeNosniff: true
        forceSTSHeader: true
        stsIncludeSubdomains: true
        stsPreload: true
        stsSeconds: 31536000
        customFrameOptionsValue: "SAMEORIGIN"
        customRequestHeaders:
          X-Forwarded-Proto: "https"
        customResponseHeaders:
          X-Robots-Tag: "none,noarchive,nosnippet,notranslate,noimageindex"
          server: ""
          X-Powered-By: ""

    # CORS middleware for API endpoints
    api-cors:
      headers:
        accessControlAllowMethods:
          - GET
          - POST
          - PUT
          - DELETE
          - OPTIONS
          - PATCH
        accessControlAllowOriginList:
          - "http://localhost:3000"
          - "http://localhost:8201"
          - "http://127.0.0.1:3000"
          - "http://127.0.0.1:8201"
          - "https://eko-api2.nextai.asia"
        accessControlAllowHeaders:
          - "*"
        accessControlExposeHeaders:
          - "*"
        accessControlAllowCredentials: true
        accessControlMaxAge: 100
        addVaryHeader: true

    # Rate limiting for API endpoints
    api-ratelimit:
      rateLimit:
        average: 100
        period: 1m
        burst: 50

    # WebSocket specific headers with session forcing for dedicated instances
    websocket-headers:
      headers:
        customRequestHeaders:
          Connection: "upgrade"
          Upgrade: "websocket"
          X-Session-Key: "document-processing"
          X-Instance-Type: "document-processing"
        customResponseHeaders:
          X-WebSocket-Enabled: "true"
          X-Instance-Type: "document-processing"
          Set-Cookie: "eko-docs-session=doc-session; Path=/; SameSite=None; Secure=false"

    # Document processing headers with session forcing for dedicated instances
    document-headers:
      headers:
        customRequestHeaders:
          X-Session-Key: "document-processing"
          X-Instance-Type: "document-processing"
        customResponseHeaders:
          X-Document-Processing: "true"
          X-Instance-Type: "document-processing"
          Set-Cookie: "eko-docs-session=doc-session; Path=/; SameSite=None; Secure=false"

    # Compression middleware
    compression:
      compress: {}

    # Retry middleware for resilience
    retry:
      retry:
        attempts: 3
        initialInterval: 100ms

    # Circuit breaker for fault tolerance
    circuit-breaker:
      circuitBreaker:
        expression: "NetworkErrorRatio() > 0.30"
        checkPeriod: 3s
        fallbackDuration: 10s
        recoveryDuration: 3s
