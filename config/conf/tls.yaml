# TLS Configuration for Traefik
# Uses existing Let's Encrypt certificates

tls:
  certificates:
    - certFile: /etc/letsencrypt/live/cert/fullchain.pem
      keyFile: /etc/letsencrypt/live/cert/privkey.pem
  
  # TLS options for better security
  options:
    default:
      minVersion: "VersionTLS12"
      cipherSuites:
        - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
        - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
        - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
        - "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256"
      curvePreferences:
        - "CurveP521"
        - "CurveP384"
      sniStrict: false
